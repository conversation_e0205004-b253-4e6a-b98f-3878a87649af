import { player } from './player.js';
import { renderScene, showModal, closeModal, setCurrentModals, initializeUI } from './ui.js';
import { gameErrorHandler } from './errorHandler.js';
import { gameState } from './gameState.js';

let currentChapterData;
let currentChapterNumber = 1;
let previousChapterNumber = 1;

// Make UI functions and player globally accessible for inline HTML onclicks
window.ui = { showModal, closeModal };
window.player = player;
window.main = { makeChoice, retryCurrentChapter, goToPreviousChapter }; // Expose functions globally

// This is the function that will be passed to the UI module
function makeChoice(choice) {
    if (choice.leadsTo === "LOAD_NEXT_CHAPTER") {
        currentChapterNumber = choice.chapter;
        loadChapter(currentChapterNumber);
    } else {
        // We pass 'makeChoice' again so the next scene also knows how to handle choices
        renderScene(choice.leadsTo, currentChapterData[choice.leadsTo], makeChoice);
    }

    // Auto-save after each choice
    gameState.save();
}

async function loadChapter(chapterNumber, retryCount = 0) {
    const maxRetries = 3;

    try {
        // Show a friendly loading message
        const storyContainer = document.getElementById('story-container');
        storyContainer.innerHTML = `
            <div class="system-prompt">
                <div class="prompt-title">[LOADING CHAPTER ${chapterNumber}]</div>
                <p>🔄 The System is accessing chapter data...</p>
                ${retryCount > 0 ? `<p><em>Retry attempt ${retryCount}/${maxRetries}</em></p>` : ''}
            </div>
        `;

        // Validate chapter number
        if (!isValidChapterNumber(chapterNumber)) {
            throw new Error(`Invalid chapter number: ${chapterNumber}`);
        }

        // Try to load the chapter with timeout
        const chapterModule = await Promise.race([
            import(`../story/chapter${chapterNumber}.js`),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Loading timeout')), 10000)
            )
        ]);

        // Validate chapter data
        if (!chapterModule.chapterData || !chapterModule.chapterData.start) {
            throw new Error('Chapter data is incomplete or corrupted');
        }

        currentChapterData = chapterModule.chapterData;
        setCurrentModals(chapterModule.modals || {});

        // Update chapter tracking
        previousChapterNumber = currentChapterNumber;
        currentChapterNumber = chapterNumber;

        console.log(`Chapter ${chapterNumber} loaded successfully.`);

        // Pass the 'makeChoice' function to the initial scene rendering
        renderScene('start', currentChapterData['start'], makeChoice);

        // Show success notification
        gameErrorHandler.showSuccess(`Chapter ${chapterNumber} loaded! 🎮`);

        // Save progress after successful load
        gameState.save();

    } catch (error) {
        console.error("Failed to load chapter:", error);

        // Try automatic recovery
        if (retryCount < maxRetries) {
            console.log(`Retrying chapter ${chapterNumber} (attempt ${retryCount + 1}/${maxRetries})`);
            setTimeout(() => {
                loadChapter(chapterNumber, retryCount + 1);
            }, 2000);
            return;
        }

        // If all retries failed, try fallback strategies
        if (await tryChapterFallbacks(chapterNumber, error)) {
            return;
        }

        // Use our beautiful error handler as last resort
        gameErrorHandler.handleError('chapter-missing', `Chapter ${chapterNumber} (${error.message})`);
    }
}

function isValidChapterNumber(chapterNumber) {
    return Number.isInteger(chapterNumber) && chapterNumber >= 1 && chapterNumber <= 20;
}

async function tryChapterFallbacks(chapterNumber, originalError) {
    console.log('Attempting chapter fallback strategies...');

    // Strategy 1: Try previous chapter if it exists
    if (chapterNumber > 1) {
        try {
            console.log(`Fallback: Loading previous chapter ${chapterNumber - 1}`);
            await loadChapter(chapterNumber - 1);

            // Show user-friendly message about the fallback
            setTimeout(() => {
                gameErrorHandler.showSuccess(`Loaded Chapter ${chapterNumber - 1} instead. Chapter ${chapterNumber} may still be in development! 📝`);
            }, 1000);

            return true;
        } catch (fallbackError) {
            console.log('Previous chapter fallback failed:', fallbackError);
        }
    }

    // Strategy 2: Try chapter 1 as ultimate fallback
    if (chapterNumber !== 1) {
        try {
            console.log('Ultimate fallback: Loading Chapter 1');
            await loadChapter(1);

            setTimeout(() => {
                gameErrorHandler.showSuccess(`Returned to Chapter 1. Chapter ${chapterNumber} is not available yet! 🔄`);
            }, 1000);

            return true;
        } catch (ultimateFallbackError) {
            console.log('Chapter 1 fallback failed:', ultimateFallbackError);
        }
    }

    // Strategy 3: Show emergency content
    showEmergencyContent(chapterNumber, originalError);
    return true;
}

function showEmergencyContent(chapterNumber, error) {
    const storyContainer = document.getElementById('story-container');
    storyContainer.innerHTML = `
        <div class="system-prompt">
            <div class="prompt-title">[EMERGENCY PROTOCOL ACTIVATED]</div>
            <p>🚨 <strong>Chapter ${chapterNumber} is currently unavailable.</strong></p>
            <p>This might happen because:</p>
            <ul>
                <li>The chapter is still being written by Gemini</li>
                <li>There's a temporary loading issue</li>
                <li>The chapter file has an error</li>
            </ul>
            <p><em>Don't worry - your progress is safe!</em></p>
        </div>
        <div class="choice-section">
            <button class="choice-button" onclick="window.main.goToPreviousChapter()">Go to Previous Chapter</button>
            <button class="choice-button" onclick="window.main.startNewGame()">Restart from Chapter 1</button>
            <button class="choice-button" onclick="window.helpSystem.showHelp('troubleshooting')">Get Help</button>
        </div>
    `;
}

// Helper functions for error recovery
function retryCurrentChapter() {
    loadChapter(currentChapterNumber);
}

function goToPreviousChapter() {
    if (previousChapterNumber && previousChapterNumber !== currentChapterNumber) {
        loadChapter(previousChapterNumber);
    } else {
        // If no previous chapter, go to chapter 1
        loadChapter(1);
    }
}

// Initialize game with save/load functionality
function initializeGame() {
    initializeUI();

    // Check for existing save data
    const saveInfo = gameState.getSaveInfo();

    if (saveInfo) {
        // Show restore option to player
        showRestoreDialog(saveInfo);
    } else {
        // Start new game
        startNewGame();
    }
}

function showRestoreDialog(saveInfo) {
    const storyContainer = document.getElementById('story-container');
    storyContainer.innerHTML = `
        <div class="system-prompt">
            <div class="prompt-title">[SAVE DATA DETECTED]</div>
            <p>🎮 <strong>Previous game session found!</strong></p>
            <p><strong>Chapter:</strong> ${saveInfo.chapter}</p>
            <p><strong>Last played:</strong> ${saveInfo.timeAgo}</p>
            <p><strong>Stats:</strong> CP: ${saveInfo.playerStats.cp || 0}, SP: ${saveInfo.playerStats.sp || 0}, DXP: ${saveInfo.playerStats.dxp || 0}</p>
        </div>
        <div class="choice-section">
            <button class="choice-button" onclick="window.main.continueGame()">Continue Previous Game</button>
            <button class="choice-button" onclick="window.main.startNewGame()">Start New Game</button>
        </div>
    `;
}

function continueGame() {
    const savedState = gameState.load();
    if (savedState && gameState.restore(savedState)) {
        loadChapter(savedState.currentChapter);
        gameErrorHandler.showSuccess('Game restored! Welcome back! 🎮');
    } else {
        gameErrorHandler.handleError('loading', 'Failed to restore save data');
        startNewGame();
    }
}

function startNewGame() {
    // Reset to default state
    gameState.resetToSafeState();
    loadChapter(1);
}

// Update global exports
window.main = {
    makeChoice,
    retryCurrentChapter,
    goToPreviousChapter,
    continueGame,
    startNewGame,
    currentChapterNumber,
    previousChapterNumber
};

// Initial game start
initializeGame();